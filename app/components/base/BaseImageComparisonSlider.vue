<script setup lang="ts">
interface Props {
  beforeImage: string
  afterImage: string
  beforeLabel?: string
  afterLabel?: string
  initialPosition?: number
  height?: string
  width?: string
  promptText?: string
  showPromptOverlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  beforeLabel: 'Before',
  afterLabel: 'After',
  initialPosition: 50,
  height: '400px',
  width: '100%',
  promptText: '',
  showPromptOverlay: false
})

const sliderPosition = ref(props.initialPosition)
const isDragging = ref(false)
const containerRef = ref<HTMLElement>()
const isHovered = ref(false)

// Emit events for parent components
const emit = defineEmits<{
  mouseenter: []
  mouseleave: []
  tryPrompt: [promptText: string]
}>()

const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  updatePosition(event)
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    updatePosition(event)
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const handleTouchStart = (event: TouchEvent) => {
  isDragging.value = true
  updatePositionTouch(event)
}

const handleTouchMove = (event: TouchEvent) => {
  if (isDragging.value) {
    event.preventDefault()
    updatePositionTouch(event)
  }
}

const handleTouchEnd = () => {
  isDragging.value = false
}

const updatePosition = (event: MouseEvent) => {
  if (!containerRef.value) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
  sliderPosition.value = percentage
}

const updatePositionTouch = (event: TouchEvent) => {
  if (!containerRef.value || !event.touches[0]) return

  const rect = containerRef.value.getBoundingClientRect()
  const x = event.touches[0].clientX - rect.left
  const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))
  sliderPosition.value = percentage
}

// Handle mouse enter/leave events
const handleMouseEnter = () => {
  isHovered.value = true
  emit('mouseenter')
}

const handleMouseLeave = () => {
  isHovered.value = false
  emit('mouseleave')
}

// Handle try prompt button click
const handleTryPrompt = () => {
  if (props.promptText) {
    emit('tryPrompt', props.promptText)
  }
}

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script>

<template>
  <div
    ref="containerRef"
    class="relative overflow-hidden rounded-lg select-none"
    :class="{ 'cursor-ew-resize': !props.showPromptOverlay }"
    :style="{ height: props.height, width: props.width }"
    @mousedown="!props.showPromptOverlay && handleMouseDown"
    @touchstart="!props.showPromptOverlay && handleTouchStart"
    @touchmove="!props.showPromptOverlay && handleTouchMove"
    @touchend="!props.showPromptOverlay && handleTouchEnd"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- After Image (Background) -->
    <div class="absolute inset-0">
      <img
        :src="props.afterImage"
        :alt="props.afterLabel"
        class="w-full h-full object-cover"
        draggable="false"
      >
      <!-- After Label -->
      <div class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
        {{ props.afterLabel }}
      </div>
    </div>

    <!-- Before Image (Clipped) - Only show when not in prompt overlay mode -->
    <div
      v-if="!props.showPromptOverlay"
      class="absolute inset-0 overflow-hidden"
      :style="{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }"
    >
      <img
        :src="props.beforeImage"
        :alt="props.beforeLabel"
        class="w-full h-full object-cover"
        draggable="false"
      >
      <!-- Before Label -->
      <div class="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
        {{ props.beforeLabel }}
      </div>
    </div>

    <!-- Slider Line - Only show when not in prompt overlay mode -->
    <div
      v-if="!props.showPromptOverlay"
      class="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10 pointer-events-none"
      :style="{ left: `${sliderPosition}%` }"
    >
      <!-- Slider Handle -->
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-auto">
        <div class="w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center cursor-ew-resize">
          <div class="flex space-x-0.5">
            <div class="w-0.5 h-4 bg-gray-400 rounded-full" />
            <div class="w-0.5 h-4 bg-gray-400 rounded-full" />
          </div>
        </div>
      </div>
    </div>

    <!-- Text Prompt Overlay -->
    <div
      v-if="props.showPromptOverlay && props.promptText"
      class="absolute bottom-0 left-0 right-0 p-4 z-20"
    >
      <div class="bg-black/40 backdrop-blur-sm rounded-lg p-4 mx-auto max-w-2xl">
        <div class="text-center">
          <div class="flex items-center justify-center mb-2">
            <UIcon
              name="i-lucide-type"
              class="w-4 h-4 text-white mr-2"
            />
            <span class="text-xs font-medium text-white/80">Text Prompt</span>
          </div>
          <p class="text-white text-sm font-medium leading-relaxed line-clamp-1 mb-2">
            "{{ props.promptText }}"
          </p>
          <UButton
            size="xs"
            color="primary"
            variant="solid"
            class="bg-primary-500 hover:bg-primary-600 text-white px-4  rounded-lg font-medium transition-all duration-200"
            @click="handleTryPrompt"
          >
            <UIcon
              name="i-lucide-sparkles"
              class="w-4 h-4 mr-1"
            />
            {{ $t('Try') }}
          </UButton>
        </div>
      </div>
    </div>

    <!-- Hover Instructions -->
    <div
      v-if="!props.showPromptOverlay"
      class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-xs opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"
    >
      Drag to compare
    </div>
  </div>
</template>

<style scoped>
/* Prevent text selection during drag */
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Smooth transitions */
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
