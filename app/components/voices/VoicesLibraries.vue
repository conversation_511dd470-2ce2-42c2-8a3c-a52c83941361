<template>
  <UCard
    :ui="{
      body: '!p-0'
    }"
    class="h-fit"
  >
    <div class="px-5 pt-6 pb-0">
      <UTabs
        v-model="voiceTypeActive"
        :items="voiceTypes"
        class="w-full"
        color="neutral"
        :content="false"
        size="sm"
      />
    </div>
    <!-- Filter Controls -->
    <div class="px-5 py-3 border-b border-gray-200 dark:border-neutral-700">
      <div class="flex flex-col gap-3">
        <div class="flex flex-row gap-2 items-center">
          <!-- Search Input -->
          <UInput
            v-model="searchQuery"
            :placeholder="$t('Search...')"
            icon="i-heroicons-magnifying-glass-20-solid"
            size="sm"
            class="flex-1"
          >
            <template #trailing>
              <UButton
                v-show="searchQuery !== ''"
                color="neutral"
                variant="link"
                icon="i-heroicons-x-mark-20-solid"
                :padded="false"
                @click="searchQuery = ''"
              />
            </template>
          </UInput>
          <!-- Country/Accent Filter -->
          <USelectMenu
            v-model="selectedAccent"
            :items="accentOptions"
            placeholder="Country"
            value-key="value"
            size="sm"
            class="w-1/4 md:w-fit md:min-w-32"
            :ui="{
              content: 'w-52'
            }"
          />

          <!-- Gender Filter -->
          <USelectMenu
            v-model="selectedGender"
            :items="genderOptions"
            placeholder="Gender"
            value-key="value"
            size="sm"
            class="w-1/4 md:w-fit md:min-w-28"
          />

          <!-- Reset Button -->
          <UButton
            color="neutral"
            variant="outline"
            size="sm"
            icon="i-heroicons-arrow-path-20-solid"
            :disabled="!hasActiveFilters"
            @click="resetFilters"
          >
            <span class="hidden sm:inline">
              {{ $t("Reset") }}
            </span>
          </UButton>
        </div>
      </div>
    </div>
    <div
      ref="scrollContainer"
      class="overflow-y-auto thin-scrollbar relative pb-4"
      :class="{ 'h-[calc(100vh-170px)]': fullScreen, 'max-h-[64vh]': !fullScreen }"
      @scroll="handleScroll"
    >
      <!-- Sticky selected voice at top -->
      <div
        v-if="selectedVoice && shouldShowStickyTop"
        class="sticky -top-1 left-0 w-full px-5 py-2 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-neutral-700 z-10"
      >
        <VoiceCard
          :voice="selectedVoice"
          variant="soft"
          class="ring-1 ring-primary-500"
          mini
          :is-playing="playingVoices.has(selectedVoice.id)"
          @click="scrollToSelectedVoice"
          @play-preview="togglePlayPreview"
        />
      </div>
      <div v-if="loading">
        <VoiceCardLoading
          v-for="i in 10"
          :key="i"
        />
      </div>
      <div
        v-else-if="voicesFiltered.length > 0"
        class="px-5 py-2 flex flex-col gap-4"
      >
        <div
          v-if="voiceTypeActive === 'my_voices'"
          class="mt-3 flex flex-row items-center gap-3 ring ring-neutral-200 dark:ring-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-800 rounded-xl px-4 py-4 text-center text-neutral-500 cursor-pointer hover:text-primary-400"
          @click="openCustomVoiceModal"
        >
          <UIcon
            name="simple-line-icons:plus"
            class="text-3xl"
          />
          <div class="text-sm">
            {{ $t("Create a new voice") }}
          </div>
        </div>
        <VoiceCard
          v-for="voice in voicesFiltered"
          :key="voice.id"
          :ref="(el) => setVoiceCardRef(voice.id, el)"
          :voice="voice"
          :class="{ 'ring-1 ring-primary-500': selectedVoice?.id === voice.id }"
          :is-playing="playingVoices.has(voice.id)"
          @click="selectVoice(voice)"
          @play-preview="togglePlayPreview"
          @delete-voice="handleDeleteVoice"
        />
      </div>
      <!-- Login Prompt for My Voices / Favorite Voices -->
      <div
        v-else-if="shouldShowLoginPrompt"
        class="flex flex-col items-center justify-center h-full text-center px-8 py-12"
      >
        <UIcon
          name="material-symbols:login"
          class="text-6xl mb-4 text-primary-500"
        />
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {{
            voiceTypeActive === "my_voices"
              ? $t("Access Your Personal Voices")
              : $t("Access Your Favorite Voices")
          }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6 max-w-md">
          {{
            voiceTypeActive === "my_voices"
              ? $t(
                "Login to view and manage your personal voice collection. Upload custom voices and access them anytime."
              )
              : $t(
                "Login to view your favorite voices. Save voices you love and access them quickly for your projects."
              )
          }}
        </p>
        <div class="flex flex-col sm:flex-row gap-3">
          <UButton
            :label="$t('Login')"
            color="primary"
            icon="material-symbols:login"
            size="lg"
            @click="goToLogin"
          />
          <UButton
            :label="$t('Create Account')"
            color="neutral"
            variant="outline"
            icon="material-symbols:person-add"
            size="lg"
            @click="goToRegister"
          />
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-4">
          {{
            $t("Join thousands of creators using AI voices for their projects")
          }}
        </p>
      </div>
      <!-- Account Activation Prompt for My Voices / Favorite Voices -->
      <div
        v-else-if="shouldShowActivationPrompt"
        class="flex flex-col items-center justify-center h-full text-center px-8 py-12"
      >
        <UIcon
          name="material-symbols:mark-email-unread"
          class="text-6xl mb-4 text-orange-500"
        />
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {{ $t("Account Activation Required") }}
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-6 max-w-md">
          {{
            voiceTypeActive === "my_voices"
              ? $t(
                "Please activate your account to access your personal voice collection. Check your email for the activation link."
              )
              : $t(
                "Please activate your account to access your favorite voices. Check your email for the activation link."
              )
          }}
        </p>
        <div class="flex flex-col sm:flex-row gap-3">
          <UButton
            :label="$t('Resend Activation Email')"
            color="primary"
            icon="material-symbols:mark-email-unread"
            size="lg"
            :loading="authStore.loading"
            @click="resendActivationEmail"
          />
          <UButton
            :label="$t('Check Email')"
            color="neutral"
            variant="outline"
            icon="material-symbols:mail"
            size="lg"
            @click="openEmailClient"
          />
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-4">
          {{
            $t("Didn't receive the email? Check your spam folder or try resending.")
          }}
        </p>
      </div>
      <!-- Regular Empty State -->
      <div
        v-else-if="voiceTypeActive === 'my_voices'"
        class="flex flex-col items-center justify-center h-full"
      >
        <div
          class=" hover:bg-neutral-50 dark:hover:bg-neutral-800 rounded-lg px-10 py-6 text-center text-primary-500 cursor-pointer hover:text-primary-400"
          @click="openCustomVoiceModal"
        >
          <UIcon
            name="simple-line-icons:plus"
            class="text-5xl mb-2"
          />
          <div class="text-sm">
            {{ $t("Create a new voice") }}
          </div>
        </div>
      </div>
      <div
        v-else
        class="flex flex-col items-center justify-center h-full text-neutral-500"
      >
        <UIcon
          name="ix:box-open"
          class="text-6xl mb-2"
        />
        <div class="text-sm">
          {{ $t("No voices found") }}
        </div>
      </div>
      <!-- Sticky selected voice at bottom -->
      <div
        v-if="selectedVoice && shouldShowStickyBottom"
        class="sticky -bottom-1 left-0 w-full px-5 py-2 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-neutral-700 z-10"
      >
        <VoiceCard
          :voice="selectedVoice"
          variant="soft"
          class="ring-1 ring-primary-500"
          mini
          :is-playing="playingVoices.has(selectedVoice.id)"
          @click="scrollToSelectedVoice"
          @play-preview="togglePlayPreview"
        />
      </div>
    </div>
  </UCard>

  <!-- Custom Voice Registration Modal -->
  <CustomVoiceRegistrationModal
    v-model="showCustomVoiceModal"
    @voice-created="handleVoiceCreated"
  />

  <!-- Delete Voice Confirmation Modal -->
  <UModal v-model:open="showDeleteModal">
    <template #content>
      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon
              name="i-heroicons-exclamation-triangle-20-solid"
              class="w-5 h-5 text-red-500"
            />
            <h3 class="text-lg font-semibold">
              {{ $t("Delete Voice") }}
            </h3>
          </div>
        </template>

        <div class="space-y-4">
          <p class="text-gray-600 dark:text-gray-400">
            {{
              $t(
                "Are you sure you want to delete this voice? This action cannot be undone."
              )
            }}
          </p>

          <div
            v-if="voiceToDelete"
            class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div
                class="h-8 w-8 bg-neutral-100 dark:bg-neutral-800 rounded-full flex items-center justify-center"
              >
                <UIcon
                  :name="voiceToDelete.icon"
                  class="w-5 h-5 text-white"
                />
              </div>
              <div>
                <div class="font-medium">
                  {{ voiceToDelete.speaker_name }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ voiceToDelete.description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton
              variant="outline"
              @click="showDeleteModal = false"
            >
              {{ $t("Cancel") }}
            </UButton>
            <UButton
              color="error"
              :loading="deletingVoice"
              @click="confirmDeleteVoice"
            >
              {{ $t("Delete") }}
            </UButton>
          </div>
        </template>
      </UCard>
    </template>
  </UModal>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    fullScreen?: boolean
    showOnlyGeminiVoices?: boolean
  }>(),
  {
    fullScreen: false,
    showOnlyGeminiVoices: false
  }
)

const emits = defineEmits<{
  selectVoice: [voice: SpeechVoice]
}>()

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const { isAuthenticated, isNotVerifyAccount } = storeToRefs(authStore)
const { selectedVoice, loadVoices, voices, loading, voiceAccents }
  = useSpeechVoices()

// Filter states
const searchQuery = ref('')
const selectedAccent = ref<string | null>(null)
const selectedGender = ref<string | null>(null)

// Refs for sticky functionality
const scrollContainer = ref<HTMLElement>()
const voiceCardRefs = ref<Map<string, HTMLElement>>(new Map())
const isSelectedVoiceVisible = ref(true)
const selectedVoicePosition = ref<'top' | 'bottom' | 'visible'>('visible')
const playingVoices = ref<Set<string>>(new Set())
const currentAudio = ref<HTMLAudioElement | null>(null)

// Custom voice modal state
const showCustomVoiceModal = ref(false)

// Delete voice modal state
const showDeleteModal = ref(false)
const voiceToDelete = ref<SpeechVoice | null>(null)
const deletingVoice = ref(false)

// Filter options
const accentOptions = computed(() => {
  const allOption = {
    value: null,
    label: t('All Countries'),
    icon: 'i-heroicons-globe-alt'
  }
  const accents = voiceAccents().map(accent => ({
    value: accent.value,
    label: accent.label,
    icon: accent.icon
  }))
  return [allOption, ...accents]
})

const genderOptions = computed(() => [
  { value: null, label: t('All Genders') },
  { value: 'male', label: t('Male') },
  { value: 'female', label: t('Female') }
])

const hasActiveFilters = computed(() => {
  return (
    searchQuery.value !== ''
    || selectedAccent.value !== null
    || selectedGender.value !== null
  )
})

// Check if user needs to login for current tab
const needsAuthentication = computed(() => {
  const authRequiredTabs = ['my_voices', 'favorite_voices']
  return (
    authRequiredTabs.includes(voiceTypeActive.value)
    && !isAuthenticated.value
  )
})

// Check if user is authenticated but account not activated
const needsAccountActivation = computed(() => {
  const authRequiredTabs = ['my_voices', 'favorite_voices']
  return (
    authRequiredTabs.includes(voiceTypeActive.value)
    && isAuthenticated.value
    && isNotVerifyAccount.value
  )
})

// Check if should show login prompt instead of empty state
const shouldShowLoginPrompt = computed(() => {
  return (
    needsAuthentication.value
    && voicesFiltered.value.length === 0
    && !loading.value
  )
})

// Check if should show activation prompt instead of empty state
const shouldShowActivationPrompt = computed(() => {
  return (
    needsAccountActivation.value
    && voicesFiltered.value.length === 0
    && !loading.value
  )
})

// Navigation functions
const goToLogin = () => {
  router.push('/auth/login')
}

const goToRegister = () => {
  router.push('/auth/signup')
}

// Account activation functions
const resendActivationEmail = async () => {
  try {
    await authStore.resendVerificationEmail()
    // Show success notification
    const toast = useToast()
    toast.add({
      title: t('Activation email sent'),
      description: t('Please check your email for the activation link.'),
      color: 'success',
      icon: 'material-symbols:mark-email-unread'
    })
  } catch (error) {
    // Error will be handled by the store and displayed in UI
    console.error('Failed to resend activation email:', error)
  }
}

const openEmailClient = () => {
  // Try to open default email client
  window.location.href = 'mailto:'
}

const voiceTypes = computed(() => {
  // If showOnlyGeminiVoices is true, only show Gemini Voices tab
  if (props.showOnlyGeminiVoices) {
    return [
      {
        label: t('Gemini Voices'),
        value: 'gemini_voice',
        icon: 'ri:gemini-fill'
      }
    ]
  }

  return [
    {
      label: t('Gemini Voices'),
      value: 'gemini_voice',
      icon: 'ri:gemini-fill'
    },
    {
      label: t('System Voices'),
      value: 'system_voice',
      icon: 'hugeicons:voice'
    },
    {
      label: t('My Voices'),
      value: 'my_voices',
      icon: 'fluent:person-voice-24-filled'
    },
    {
      label: t('Favorite Voices'),
      value: 'favorite_voices',
      icon: 'mdi:puzzle-favorite'
    }
  ]
})

const voiceTypeActive = computed({
  get() {
    // If showOnlyGeminiVoices is true, always return 'gemini_voice'
    if (props.showOnlyGeminiVoices) {
      return 'gemini_voice'
    }
    return (route.query.voiceType as string) || 'gemini_voice'
  },
  set(voiceType) {
    // If showOnlyGeminiVoices is true, don't allow changing voice type
    if (props.showOnlyGeminiVoices) {
      return
    }
    // Hash is specified here to prevent the page from scrolling to the top
    router.push({
      query: {
        ...route.query,
        voiceType
      }
    })
  }
})

const voicesFiltered = computed(() => {
  let filtered = voices.value

  // If showOnlyGeminiVoices is true, always filter to only Gemini voices
  if (props.showOnlyGeminiVoices) {
    filtered = filtered.filter(voice => voice.type === 'gemini_voice')
  } else {
    // Filter by voice type
    if (voiceTypeActive.value === 'gemini_voice') {
      filtered = filtered.filter(voice => voice.type === 'gemini_voice')
    } else if (voiceTypeActive.value === 'system_voice') {
      filtered = filtered.filter(voice => voice.type === 'system_voice')
    } else if (voiceTypeActive.value === 'my_voices') {
      filtered = filtered.filter(voice => voice.type === 'user_voice')
    } else if (voiceTypeActive.value === 'favorite_voices') {
      filtered = filtered.filter(voice => voice.is_favorite)
    }
  }

  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(
      voice =>
        voice.speaker_name.toLowerCase().includes(query)
        || voice.description.toLowerCase().includes(query)
    )
  }

  // Filter by accent/country
  if (selectedAccent.value) {
    filtered = filtered.filter(
      voice =>
        voice.accent.toLowerCase() === selectedAccent.value?.toLowerCase()
    )
  }

  // Filter by gender
  if (selectedGender.value) {
    filtered = filtered.filter(
      voice =>
        voice.gender.toLowerCase() === selectedGender.value?.toLowerCase()
    )
  }

  return filtered
})

// Computed properties for sticky visibility
const shouldShowStickyTop = computed(() => {
  return (
    selectedVoice.value
    && !isSelectedVoiceVisible.value
    && selectedVoicePosition.value === 'top'
    && selectedVoice.value.type === voiceTypeActive.value
  )
})

const shouldShowStickyBottom = computed(() => {
  return (
    selectedVoice.value
    && !isSelectedVoiceVisible.value
    && selectedVoicePosition.value === 'bottom'
    && selectedVoice.value.type === voiceTypeActive.value
  )
})

// Functions for sticky functionality
const setVoiceCardRef = (voiceId: string, el: any) => {
  if (el) {
    voiceCardRefs.value.set(voiceId, el.$el || el)
  } else {
    voiceCardRefs.value.delete(voiceId)
  }
}

const handleScroll = () => {
  if (!selectedVoice.value || !scrollContainer.value) return

  const selectedElement = voiceCardRefs.value.get(selectedVoice.value.id)
  if (!selectedElement) return

  const containerRect = scrollContainer.value.getBoundingClientRect()
  const elementRect = selectedElement.getBoundingClientRect()

  // Check if the selected voice card is visible in the scroll container
  const isVisible
    = elementRect.top >= containerRect.top
      && elementRect.bottom <= containerRect.bottom

  isSelectedVoiceVisible.value = isVisible

  if (!isVisible) {
    // Determine if the selected voice is above or below the visible area
    if (elementRect.top < containerRect.top) {
      selectedVoicePosition.value = 'top'
    } else {
      selectedVoicePosition.value = 'bottom'
    }
  } else {
    selectedVoicePosition.value = 'visible'
  }
}

const scrollToSelectedVoice = () => {
  if (!selectedVoice.value || !scrollContainer.value) return

  const selectedElement = voiceCardRefs.value.get(selectedVoice.value.id)
  if (!selectedElement) return

  selectedElement.scrollIntoView({
    behavior: 'smooth',
    block: 'center'
  })
}

const selectVoice = (voice: SpeechVoice) => {
  // Use store method to ensure proper icon handling
  const { selectVoice: storeSelectVoice } = useSpeechVoices()
  storeSelectVoice(voice)
  emits('selectVoice', selectedVoice.value || voice)
  // Check visibility after selection
  nextTick(() => {
    handleScroll()
  })
}

// Filter functions
const resetFilters = () => {
  searchQuery.value = ''
  selectedAccent.value = null
  selectedGender.value = null
}

// Custom voice modal functions
const openCustomVoiceModal = () => {
  showCustomVoiceModal.value = true
}

const handleVoiceCreated = (_voice: any) => {
  // Reload voices to include the new custom voice
  loadVoices()
}

// Delete voice functions
const handleDeleteVoice = (voice: SpeechVoice) => {
  voiceToDelete.value = voice
  showDeleteModal.value = true
}

const confirmDeleteVoice = async () => {
  if (!voiceToDelete.value) return

  deletingVoice.value = true
  const { deleteCustomVoice } = useSpeechVoices()
  const toast = useToast()

  try {
    const success = await deleteCustomVoice(voiceToDelete.value.id)

    if (success) {
      toast.add({
        title: t('Success'),
        description: t('Voice deleted successfully'),
        color: 'success'
      })
      showDeleteModal.value = false
      voiceToDelete.value = null
    } else {
      toast.add({
        title: t('Error'),
        description: t('Failed to delete voice'),
        color: 'error'
      })
    }
  } catch (error) {
    console.error('Error deleting voice:', error)
    toast.add({
      title: t('Error'),
      description: t('Failed to delete voice'),
      color: 'error'
    })
  } finally {
    deletingVoice.value = false
  }
}

onMounted(() => {
  loadVoices()
})

// Cleanup on unmount
onUnmounted(() => {
  stopAllAudio()
})

// Watch for voice type changes to reset scroll position
watch(voiceTypeActive, () => {
  nextTick(() => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = 0
    }
    handleScroll()
  })
})

// Audio functions
const stopAllAudio = () => {
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
  }
  playingVoices.value.clear()
}

const togglePlayPreview = (voice: SpeechVoice) => {
  if (!voice.sample_audio_path) return

  // If this voice is currently playing, stop it
  if (playingVoices.value.has(voice.id)) {
    stopAllAudio()
    return
  }

  // Stop any currently playing audio
  stopAllAudio()

  try {
    const audio = new Audio(voice.sample_audio_path)
    currentAudio.value = audio
    playingVoices.value.add(voice.id)

    audio.addEventListener('ended', () => {
      playingVoices.value.delete(voice.id)
      if (currentAudio.value === audio) {
        currentAudio.value = null
      }
    })

    audio.addEventListener('error', () => {
      playingVoices.value.delete(voice.id)
      if (currentAudio.value === audio) {
        currentAudio.value = null
      }
    })

    audio.play().catch((err) => {
      console.error('Failed to play audio preview:', err)
      playingVoices.value.delete(voice.id)
      if (currentAudio.value === audio) {
        currentAudio.value = null
      }
    })
  } catch (err) {
    console.error('Failed to create audio element:', err)
    playingVoices.value.delete(voice.id)
  }
}
</script>
